package com.botong.system.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.system.query.BillExpensesListQuery;
import com.botong.system.service.BillExpensesListService;
import com.botong.system.vo.MaintenanceBillVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
* 运维费账单
*
* <AUTHOR> 
* @since 1.0.0 2025-01-09
*/
@RestController
@RequestMapping("maintenance")
@Tag(name="运维费账单")
@AllArgsConstructor
public class BillExpensesListController {

    private final BillExpensesListService billExpensesListService;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('system:maintenance:page')")
    public Result<PageResult<MaintenanceBillVO>> page(@Valid BillExpensesListQuery query){
        PageResult<MaintenanceBillVO> page = billExpensesListService.page(query);
        return Result.ok(page);
    }

    @GetMapping("export")
    @Operation(summary = "导出运维账单")
    @PreAuthorize("hasAuthority('system:maintenance:export')")
    public void export() {
        billExpensesListService.export();
    }


}