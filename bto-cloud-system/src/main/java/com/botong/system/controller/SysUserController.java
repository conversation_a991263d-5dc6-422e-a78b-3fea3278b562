package com.botong.system.controller;

import cn.hutool.core.util.StrUtil;
import com.botong.api.module.system.vo.RepairUserVO;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.common.utils.Result;
import com.botong.framework.security.user.SecurityUser;
import com.botong.framework.security.user.UserDetail;
import com.botong.system.convert.SysUserConvert;
import com.botong.system.entity.SysUserEntity;
import com.botong.system.query.SysUserQuery;
import com.botong.system.service.SysUserOrgService;
import com.botong.system.service.SysUserPostService;
import com.botong.system.service.SysUserRoleService;
import com.botong.system.service.SysUserService;
import com.botong.system.vo.OrgWithUserVO;
import com.botong.system.vo.SysUserPasswordVO;
import com.botong.system.vo.SysUserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;


/**
 * 用户管理
 */
@Validated
@RestController
@RequestMapping("user")
@AllArgsConstructor
@Tag(name = "用户管理")
public class SysUserController {
    private final SysUserService sysUserService;
    private final SysUserRoleService sysUserRoleService;
    private final SysUserPostService sysUserPostService;
    private final SysUserOrgService sysUserOrgService;
    private final PasswordEncoder passwordEncoder;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('sys:user:page')")
    public Result<PageResult<SysUserVO>> page(@Valid @ParameterObject SysUserQuery query) {
        PageResult<SysUserVO> page = sysUserService.page(query);

        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    public Result<SysUserVO> get(@PathVariable("id") Long id) {
        SysUserEntity entity = sysUserService.getById(id);

        SysUserVO vo = SysUserConvert.INSTANCE.convert(entity);

        // 用户角色列表
        List<Long> roleIdList = sysUserRoleService.getRoleIdList(id);
        vo.setRoleIdList(roleIdList);

        // 用户岗位列表
        List<Long> postIdList = sysUserPostService.getPostIdList(id);
        vo.setPostIdList(postIdList);

        // 用户机构列表
        List<Long> orgIdList = sysUserOrgService.getOrgIdList(id);
        vo.setOrgIds(orgIdList);

        return Result.ok(vo);
    }

    @GetMapping("info")
    @Operation(summary = "登录用户")
    public Result<SysUserVO> info() {
        SysUserVO user = sysUserService.info();

        return Result.ok(user);
    }

    @PutMapping("password")
    @Operation(summary = "修改密码")
    public Result<String> password(@RequestBody @Valid SysUserPasswordVO vo) {
        // 原密码不正确
        UserDetail user = SecurityUser.getUser();
        if (!passwordEncoder.matches(vo.getPassword(), user.getPassword())) {
            return Result.error("原密码不正确");
        }

        // 修改密码
        sysUserService.updatePassword(user.getId(), passwordEncoder.encode(vo.getNewPassword()));

        return Result.ok();
    }

    @PostMapping
    @Operation(summary = "保存")
    @PreAuthorize("hasAuthority('sys:user:save')")
    public Result<String> save(@RequestBody @Valid SysUserVO vo) {
        // 新增密码不能为空
        if (StrUtil.isBlank(vo.getPassword())) {
            Result.error("密码不能为空");
        }

        // 密码加密
        vo.setPassword(passwordEncoder.encode(vo.getPassword()));

        // 保存
        sysUserService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('sys:user:update')")
    public Result<String> update(@RequestBody @Valid SysUserVO vo) {
        // 如果密码不为空，则进行加密处理
        if (StrUtil.isBlank(vo.getPassword())) {
            vo.setPassword(null);
        } else {
            vo.setPassword(passwordEncoder.encode(vo.getPassword()));
        }

        sysUserService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('sys:user:delete')")
    public Result<String> delete(@RequestBody List<Long> idList) {
        Long userId = SecurityUser.getUserId();
        if (idList.contains(userId)) {
            return Result.error("不能删除当前登录用户");
        }
        sysUserService.delete(idList);

        return Result.ok();
    }

    @PostMapping("import")
    @Operation(summary = "导入用户")
    @PreAuthorize("hasAuthority('sys:user:import')")
    public Result<String> importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择需要上传的文件");
        }
        sysUserService.importByExcel(file, passwordEncoder.encode("123456"));

        return Result.ok();
    }

    @GetMapping("export")
    @Operation(summary = "导出用户")
    @PreAuthorize("hasAuthority('sys:user:export')")
    public void export() {
        sysUserService.export();
    }



    @GetMapping("repairUserByWorkId")
    @Operation(summary = "根据工单id获取维修人员列表")
    @PreAuthorize("hasAuthority('sys:user:repairUserByWorkId')")
    public Result<List<RepairUserVO>> repairUserByWorkId(@RequestParam Long workId) {
        return Result.ok(sysUserService.repairUserByWorkId(workId));
    }

    @GetMapping("getOrgWithUserByWorkId")
    @Operation(summary = "根据工单id获取机构人员列表")
    public Result<List<OrgWithUserVO>> getOrgWithUserByWorkId(@RequestParam Long workId) {
        return Result.ok(sysUserService.getOrgWithUserByWorkId(workId));
    }

    @GetMapping("listByRemark")
    @Operation(summary = "根据名称获取人员列表")
    @PreAuthorize("hasAuthority('sys:user:listByOrg')")
    public Result<List<RepairUserVO>> repairByName(@Parameter(description = "0->维修 1->运维") @Range(min = 0,max = 1) Integer dictValue) {
        AssertUtils.isNull(dictValue,"dictValue");
        return Result.ok(sysUserService.repairByName(dictValue));
    }

    @GetMapping("getUserListByOrgId")
    @Operation(summary = "根据机构id获取维修人员列表")
    @PreAuthorize("hasAuthority('sys:user:listByOrg')")
    public Result<List<RepairUserVO>> getUserListByOrgId(@NotNull @RequestParam("orgIdList") List<Long> orgIdList) {
        return Result.ok(sysUserService.getUserListByOrgId(orgIdList));
    }

    @GetMapping("listByRemarkWithCity")
    @Operation(summary = "根据名称获取人员城市列表")
    @PreAuthorize("hasAuthority('sys:user:listByOrg')")
    public Result<Map<String, List<RepairUserVO>>> listByRemarkWithCity(@RequestParam(value = "0->维修 1->运维",required = false) @Range(min = 0,max = 1) Integer dictValue) {
        return Result.ok(sysUserService.listByRemarkWithCity(dictValue));
    }

}
