package com.botong.system.controller;

import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.system.convert.SysAttachmentTypeConvert;
import com.botong.system.entity.SysAttachmentTypeEntity;
import com.botong.system.query.SysAttachmentQuery;
import com.botong.system.query.SysAttachmentTypeQuery;
import com.botong.system.service.SysAttachmentTypeService;
import com.botong.system.vo.SysAttachmentTypeVO;
import com.botong.system.vo.SysAttachmentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 附件分类管理
*
* <AUTHOR> 
* @since 1.0.0 2025-03-04
*/
@RestController
@RequestMapping("attachmentType")
@Tag(name="附件分类管理")
@AllArgsConstructor
public class SysAttachmentTypeController {
    private final SysAttachmentTypeService sysAttachmentTypeService;

    @GetMapping("page")
    @Operation(summary = "分页")
    // @PreAuthorize("hasAuthority('system:type:page')")
    public Result<PageResult<SysAttachmentTypeVO>> page(@Valid SysAttachmentTypeQuery query){
        PageResult<SysAttachmentTypeVO> page = sysAttachmentTypeService.page(query);

        return Result.ok(page);
    }

    @PostMapping("getAttachmentByTypeId")
    @Operation(summary = "根据分类ID分页查询附件")
    // @PreAuthorize("hasAuthority('system:type:list')")
    public Result<PageResult<SysAttachmentVO>> getAttachmentByTypeId(@RequestBody SysAttachmentQuery query){
        PageResult<SysAttachmentVO> page = sysAttachmentTypeService.getAttachmentByTypeId(query);
        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "根据id获取详情")
    // @PreAuthorize("hasAuthority('sys:type:info')")
    public Result<SysAttachmentTypeVO> get(@PathVariable("id") Long id){
        SysAttachmentTypeEntity entity = sysAttachmentTypeService.getById(id);
        return Result.ok(SysAttachmentTypeConvert.INSTANCE.convert(entity));
    }


    @PostMapping
    @Operation(summary = "保存")
    // @PreAuthorize("hasAuthority('system:type:save')")
    public Result<String> save(@RequestBody SysAttachmentTypeVO vo){
        sysAttachmentTypeService.save(vo);

        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    // @PreAuthorize("hasAuthority('system:type:update')")
    public Result<String> update(@RequestBody @Valid SysAttachmentTypeVO vo){
        sysAttachmentTypeService.update(vo);

        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    // @PreAuthorize("hasAuthority('system:type:delete')")
    public Result<String> delete(@RequestBody List<Long> idList){
        sysAttachmentTypeService.delete(idList);

        return Result.ok();
    }
}