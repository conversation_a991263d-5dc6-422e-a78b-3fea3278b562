package com.botong.workorder.controller;

import com.botong.framework.common.utils.Result;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.service.WorkOperationInfoService;
import com.botong.workorder.vo.WorkOperationInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 工单维修信息管理
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */
@RestController
@RequestMapping("operation")
@Tag(name = "工单维修信息管理")
@AllArgsConstructor
@Validated
public class WorkOperationInfoController {
    private final WorkOperationInfoService workOperationInfoService;

    @GetMapping("{id}")
    @Operation(summary = "信息")
    @PreAuthorize("hasAuthority('workorder:operation:info')")
    public Result<WorkOperationInfoVO> get(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        return Result.ok(workOperationInfoService.info(id));
    }

    @PostMapping
    @Operation(summary = "保存报价与维修信息")
    @PreAuthorize("hasAuthority('workorder:operation:save')")
    public Result<String> updateBatch(@Valid @RequestBody WorkOperationInfoDTO dto) {
        workOperationInfoService.updateBatch(dto);

        return Result.ok();
    }

    @PutMapping("updateOvertime")
    @Operation(summary = "更新超时时间")
    @PreAuthorize("hasAuthority('workorder:operation:overtime')")
    public Result<String> updateOvertime(@RequestBody List<Long> workIdList) {
        workOperationInfoService.updateOvertime(workIdList);
        return Result.ok();
    }
}