package com.botong.workorder.controller;

import com.botong.framework.common.constant.BtoConstant;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.Result;
import com.botong.workorder.convert.PlantMaterialInfoConvert;
import com.botong.workorder.entity.PlantMaterialInfoEntity;
import com.botong.workorder.query.PlantMaterialInfoQuery;
import com.botong.workorder.service.PlantMaterialInfoService;
import com.botong.workorder.vo.PlantMaterialInfoTree;
import com.botong.workorder.vo.PlantMaterialInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 电站材料管理
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@RestController
@RequestMapping("material")
@Tag(name = "电站材料管理")
@AllArgsConstructor
public class PlantMaterialInfoController {
    private final PlantMaterialInfoService plantMaterialInfoService;

    @GetMapping("page")
    @Operation(summary = "分页")
    @PreAuthorize("hasAuthority('plant:material:page')")
    public Result<PageResult<PlantMaterialInfoTree>> page(@Valid PlantMaterialInfoQuery query) {
        PageResult<PlantMaterialInfoTree> page = plantMaterialInfoService.page(query);
        return Result.ok(page);
    }

    @GetMapping("{id}")
    @Operation(summary = "信息")
    @PreAuthorize("hasAuthority('plant:material:info')")
    public Result<PlantMaterialInfoVO> get(@PathVariable("id") Long id) {
        PlantMaterialInfoEntity entity = plantMaterialInfoService.getById(id);
        if (entity == null) {
            return Result.error("找不到指定的材料信息");
        }
        PlantMaterialInfoVO vo = PlantMaterialInfoConvert.INSTANCE.convert(entity);

        // 获取上级机构名称
        if(!BtoConstant.ROOT.equals(entity.getPid())){
            PlantMaterialInfoEntity parent = plantMaterialInfoService.getById(entity.getPid());
            vo.setParentName(parent.getName());
        }

        return Result.ok(vo);
    }

    @PostMapping
    @Operation(summary = "新增")
    @PreAuthorize("hasAuthority('plant:material:add')")
    public Result<String> add(@RequestBody @Valid PlantMaterialInfoVO vo) {
        plantMaterialInfoService.save(vo);
        return Result.ok();
    }

    @PutMapping
    @Operation(summary = "修改")
    @PreAuthorize("hasAuthority('plant:material:update')")
    public Result<String> update(@RequestBody @Valid PlantMaterialInfoVO vo) {
        plantMaterialInfoService.update(vo);
        return Result.ok();
    }

    @DeleteMapping
    @Operation(summary = "删除")
    @PreAuthorize("hasAuthority('plant:material:delete')")
    public Result<String> delete(@RequestBody List<Long> idList) {
        plantMaterialInfoService.delete(idList);
        return Result.ok();
    }

    @GetMapping("list")
    @Operation(summary = "列表")
    @PreAuthorize("hasAuthority('plant:material:list')")
    public Result<List<PlantMaterialInfoTree>> list() {
        return Result.ok(plantMaterialInfoService.materialList());
    }
}