package com.botong.workorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.botong.framework.common.exception.ServerException;
import com.botong.framework.common.page.PageResult;
import com.botong.framework.common.utils.AssertUtils;
import com.botong.framework.mybatis.service.impl.BaseServiceImpl;
import com.botong.workorder.convert.BillWorkExpenseConvert;
import com.botong.workorder.convert.WorkOperationInfoConvert;
import com.botong.workorder.dao.WorkOperationInfoDao;
import com.botong.workorder.dto.PlantMaterialInfoDTO;
import com.botong.workorder.dto.WorkOperationInfoDTO;
import com.botong.workorder.entity.*;
import com.botong.workorder.enums.WorkOrderOvertimeEnum;
import com.botong.workorder.query.WorkOperationInfoQuery;
import com.botong.workorder.service.*;
import com.botong.workorder.vo.OfferPlantMaterialInfoVO;
import com.botong.workorder.vo.WorkChargeVO;
import com.botong.workorder.vo.WorkOperationInfoVO;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.botong.workorder.enums.WorkOrderOvertimeEnum.ABOUT_TO_TIMEOUT;
import static com.botong.workorder.enums.WorkOrderStatusEnum.COMPLETED;

/**
 * 工单待运维表
 *
 * <AUTHOR>
 * @since 1.0.0 2023-08-22
 */

@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class WorkOperationInfoServiceImpl extends BaseServiceImpl<WorkOperationInfoDao, WorkOperationInfoEntity> implements WorkOperationInfoService {

    @Resource
    private WorkServiceOfferService workServiceOfferService;
    @Resource
    private PlantMaterialInfoService plantMaterialInfoService;
    @Resource
    private WorkBaseInfoService workBaseInfoService;
    @Resource
    private WorkExamineInfoService workExamineInfoService;
    @Resource
    private PlantBaseInfoService plantBaseInfoService;

    private final BillWorkExpenseService billWorkExpenseService;

    private final WorkChargeService workChargeService;


    @Override
    public PageResult<WorkOperationInfoVO> page(WorkOperationInfoQuery query) {
        IPage<WorkOperationInfoEntity> page = baseMapper.selectPage(getPage(query), getWrapper(query));

        return new PageResult<>(WorkOperationInfoConvert.INSTANCE.convertList(page.getRecords()), page.getTotal());
    }

    private LambdaQueryWrapper<WorkOperationInfoEntity> getWrapper(WorkOperationInfoQuery query) {
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = Wrappers.lambdaQuery();

        return wrapper;
    }

    @Override
    public void save(WorkOperationInfoVO vo) {
        WorkOperationInfoEntity entity = WorkOperationInfoConvert.INSTANCE.convert(vo);

        baseMapper.insert(entity);
    }

    @Override
    public void update(WorkOperationInfoVO vo) {
        // 校验参数
        AssertUtils.isNull(vo.getWorkId(), "工单id");
        // 创建LambdaQueryWrapper
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = new LambdaQueryWrapper<>();
        // 设置查询条件
        wrapper.eq(WorkOperationInfoEntity::getWorkId, vo.getWorkId());
        // 转换WorkOperationInfoVO
        WorkOperationInfoEntity entity = WorkOperationInfoConvert.INSTANCE.convert(vo);
        // 更新实体
        this.update(entity, wrapper);
    }

    @Override
    public void delete(List<Long> idList) {
        removeByIds(idList);
    }

    @Override
    public WorkOperationInfoVO info(Long workId) {
        // 根据工单ID查询工单信息
        WorkOperationInfoEntity entity = this.lambdaQuery().eq(WorkOperationInfoEntity::getWorkId, workId).one();
        // 根据工单ID查询工单基本信息
        WorkBaseInfoEntity workBaseInfo = workBaseInfoService.getById(workId);
        if (ObjectUtil.isNull(workBaseInfo)) {
            throw new ServerException("尚未创建工单");
        }


        // 将实体类转换为VO
        WorkOperationInfoVO vo = BeanUtil.copyProperties(entity, WorkOperationInfoVO.class);
        vo.setAlarmType(workBaseInfo.getAlarmType());

        String plantId = workBaseInfo.getPlantId();
        PlantBaseInfoEntity plant = plantBaseInfoService.getById(plantId);
        vo.setWarrantyStatus(plant.getWarrantyStatus());

        // 获取工单的原料信息
        List<OfferPlantMaterialInfoVO> plantMaterialInfoList = getOfferPlantMaterialInfo(workId);

        if (CollUtil.isNotEmpty(plantMaterialInfoList)) {
            vo.setPlantMaterialInfoList(plantMaterialInfoList);
        }

        // 获取工单审核信息
        WorkExamineInfoEntity examineInfo = workExamineInfoService.lambdaQuery().eq(WorkExamineInfoEntity::getWorkId, workId).one();
        if (ObjectUtil.isNotNull(examineInfo)) {
            vo.setAuditResult(examineInfo.getAuditResult());
        }
        LambdaQueryWrapper<BillWorkExpenseEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillWorkExpenseEntity::getWorkId, workId);
        List<BillWorkExpenseEntity> list = billWorkExpenseService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            vo.setBillWorkExpenseList(BillWorkExpenseConvert.INSTANCE.convertList(list));
        }

        List<WorkChargeVO> workCharges = workChargeService.listByWorkId(workId);
        if (CollUtil.isNotEmpty(workCharges)) {
            vo.setWorkChargeList(workCharges);
        }
        return vo;
    }

    @NotNull
    public List<OfferPlantMaterialInfoVO> getOfferPlantMaterialInfo(Long workId) {
        // 报价List
        List<WorkServiceOfferEntity> serviceOfferList = workServiceOfferService.lambdaQuery().
                eq(WorkServiceOfferEntity::getWorkId, workId)
                .list(); // 从数据库或其他地方选出的与工单对应的服务报价数据

        if (serviceOfferList.isEmpty()) {
            return Collections.emptyList();
        }

        // 厂家List
        List<PlantMaterialInfoEntity> manufacturerList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 0L)
                .list();
        // 供应商List
        List<PlantMaterialInfoEntity> supplyList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 1L)
                .list();
        // 材料类型List
        List<PlantMaterialInfoEntity> materialTypeList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 2L)
                .list();
        // 规格List
        List<PlantMaterialInfoEntity> specificationList = plantMaterialInfoService.lambdaQuery()
                .eq(PlantMaterialInfoEntity::getType, 3L)
                .list();

        // 材料表
        List<OfferPlantMaterialInfoVO> plantMaterialInfoList = new ArrayList<>();

        // 遍历服务报价数据
        for (WorkServiceOfferEntity serviceOffer : serviceOfferList) {
            Long supplyId = null;
            Long manufacturerId = null;
            Long materialTypeId = null;

            // 创建一个材料信息对象
            OfferPlantMaterialInfoVO materialInfo = new OfferPlantMaterialInfoVO();

            // 设置材料ID和名称
            materialInfo.setId(serviceOffer.getMaterialId());
            materialInfo.setCount(serviceOffer.getCount());
            materialInfo.setTotalPrice(serviceOffer.getTotalPrice());

            // 遍历电站材料数据，根据ID匹配材料信息
            for (PlantMaterialInfoEntity specification : specificationList) {
                if (specification.getId().equals(serviceOffer.getMaterialId())) {
                    // 设置名称、类型、采购方式、价格和类型
                    materialInfo.setName(specification.getName());
                    materialInfo.setPurchaseWay(specification.getPurchaseWay());
                    materialInfo.setPrice(specification.getPrice());
                    materialInfo.setType(specification.getType());
                    materialTypeId = specification.getPid();

                }
            }

            for (PlantMaterialInfoEntity materialType : materialTypeList) {
                if (materialType.getId().equals(materialTypeId)) {
                    materialInfo.setMaterialTypeId(materialType.getId());
                    materialInfo.setMaterialTypeName(materialType.getName());
                    supplyId = materialType.getPid();
                }
            }

            for (PlantMaterialInfoEntity supply : supplyList) {
                if (supply.getId().equals(supplyId)) {
                    materialInfo.setSupplyId(supply.getId());
                    materialInfo.setSupplyName(supply.getName());
                    manufacturerId = supply.getPid();
                }
            }
            for (PlantMaterialInfoEntity manufacturer : manufacturerList) {
                if (manufacturer.getId().equals(manufacturerId)) {
                    materialInfo.setManufacturerId(manufacturer.getId());
                    materialInfo.setManufacturerName(manufacturer.getName());
                }
            }
            // 将材料信息添加到列表中
            plantMaterialInfoList.add(materialInfo);
        }
        return plantMaterialInfoList;
    }

    @Override
    public void updateBatch(WorkOperationInfoDTO dto) {
        Long workId = dto.getWorkId();
        if (ObjectUtil.isNotNull(dto.getWarrantyStatus())) {
            WorkBaseInfoEntity workBaseInfo = workBaseInfoService.getById(workId);
            AssertUtils.isNull(workBaseInfo, "工单");
            String plantId = workBaseInfo.getPlantId();
            plantBaseInfoService.lambdaUpdate()
                    .eq(PlantBaseInfoEntity::getId, plantId)
                    .set(PlantBaseInfoEntity::getWarrantyStatus, dto.getWarrantyStatus())
                    .update();
        }


        // 获取物料信息列表
        List<PlantMaterialInfoDTO> plantMaterialInfoList = dto.getPlantMaterialInfoList();
        // 在保清空报价材料
        if (dto.getWarrantyStatus() == 0 || dto.getRepairStatus() == 1) {
            plantMaterialInfoList = null;
        }

        if (dto.getRepairStatus() == 1) {
            dto.setOpCause("");
            dto.setRepairPhotoPath("");
        } else {
            dto.setUnrepairedCause("");
        }
        // 如果物料信息列表不为空
        if (CollUtil.isNotEmpty(plantMaterialInfoList)) {
            // 创建一个id和数量映射的Map
            Map<Long, Integer> idCountMap = plantMaterialInfoList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            PlantMaterialInfoDTO::getId,
                            PlantMaterialInfoDTO::getCount
                    ));

            // 获取物料id和价格映射的Map
            Map<Long, BigDecimal> idPriceMap = getIdPriceMap(dto.getPlantMaterialInfoList());

            // 如果id和数量映射的Map和价格映射的Map都不为空
            if (CollUtil.isNotEmpty(idCountMap) && CollUtil.isNotEmpty(idPriceMap)) {
                // 创建一个工作服务报价实体列表
                List<WorkServiceOfferEntity> list = new ArrayList<>();
                // 获取工作id
                // 遍历id和数量映射的Map
                for (Map.Entry<Long, Integer> entry : idCountMap.entrySet()) {
                    Long id = entry.getKey();
                    Integer count = entry.getValue();
                    // 获取价格
                    BigDecimal price = idPriceMap.get(id);
                    // 如果价格不为空
                    if (price != null) {
                        // 创建一个工作服务报价实体
                        WorkServiceOfferEntity workServiceOffer = new WorkServiceOfferEntity(workId, id, count);
                        // 计算总价
                        BigDecimal totalPrice = price.multiply(BigDecimal.valueOf(count)).setScale(2, RoundingMode.HALF_UP);
                        // 设置总价
                        workServiceOffer.setTotalPrice(totalPrice);
                        // 添加到列表
                        list.add(workServiceOffer);
                    }
                }

                // 如果工作服务报价实体列表不为空
                if (CollUtil.isNotEmpty(list)) {
                    // 更新工作服务报价实体列表
                    workServiceOfferService.lambdaUpdate()
                            .eq(WorkServiceOfferEntity::getWorkId, workId)
                            .remove();
                    workServiceOfferService.saveBatch(list);
                }
            }
        } else {
            // 更新工作服务报价实体列表
            workServiceOfferService.lambdaUpdate()
                    .eq(WorkServiceOfferEntity::getWorkId, dto.getWorkId())
                    .remove();
        }

        // 创建一个工作操作信息实体
        WorkOperationInfoEntity entity = BeanUtil.copyProperties(dto, WorkOperationInfoEntity.class);
        // 判断是否存在
        boolean exists = this.lambdaQuery()
                .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                .exists();

        // 写入同行人
        if (entity.getHasCompanion() != null && entity.getHasCompanion() == 1) {
            List<Long> companion = dto.getCompanion();
            entity.setCompanion(JSONUtil.toJsonStr(companion));
        }

        // 如果存在
        if (exists) {
            // 更新
            this.lambdaUpdate()
                    .eq(WorkOperationInfoEntity::getWorkId, entity.getWorkId())
                    .update(entity);
        } else {
            // 保存
            this.save(entity);
        }

        if (workId != null) {
            workBaseInfoService.lambdaUpdate()
                    .eq(WorkBaseInfoEntity::getId, workId)
                    .set(WorkBaseInfoEntity::getAlarmType, dto.getAlarmType()).update();
        }


    }

    @Override
    public void deleteByWorkIds(List<Long> idList) {
        LambdaQueryWrapper<WorkOperationInfoEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkOperationInfoEntity::getWorkId, idList);
        baseMapper.delete(wrapper);
    }

    private Map<Long, BigDecimal> getIdPriceMap(List<PlantMaterialInfoDTO> plantMaterialInfoList) {
        // 如果plantMaterialInfoList为空，返回空Map
        if (CollUtil.isEmpty(plantMaterialInfoList)) {
            return Collections.emptyMap();
        }

        // 获取plantMaterialInfoList中id的列表
        List<Long> idList = plantMaterialInfoList.stream()
                .map(PlantMaterialInfoDTO::getId)
                .collect(Collectors.toList());

        // 根据id列表查询plantMaterialInfoEntity
        List<PlantMaterialInfoEntity> plantMaterialInfoEntities = plantMaterialInfoService.lambdaQuery()
                .in(PlantMaterialInfoEntity::getId, idList)
                .list();

        // 如果plantMaterialInfoEntities为空，返回空Map
        if (CollUtil.isEmpty(plantMaterialInfoEntities)) {
            return Collections.emptyMap();
        }

        // 将plantMaterialInfoEntities中的id和price封装成Map
        return plantMaterialInfoEntities.stream()
                .collect(Collectors.toMap(
                        PlantMaterialInfoEntity::getId,
                        PlantMaterialInfoEntity::getPrice
                ));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateOvertime(List<Long> workIdList) {
        List<WorkOperationInfoEntity> list;
        // 创建一个Map，用来存储工单ID和状态
        Map<Long, Integer> workIdAndStatusMap = new HashMap<>();
        // 判断workIdList是否为空
        if (CollUtil.isNotEmpty(workIdList)) {
            // 查询工单ID在workIdList中的数据
            list = this.lambdaQuery()
                    .in(WorkOperationInfoEntity::getWorkId, workIdList)
                    .list();
            // 如果查询结果为空，抛出异常
            if (CollUtil.isEmpty(list)) {
                throw new ServerException("工单不存在");
            }
        } else {
            // 查询所有工单
            List<WorkBaseInfoEntity> workList = workBaseInfoService.lambdaQuery().list();
            // 将查询结果转换为Map
            workIdAndStatusMap = workList.stream().collect(Collectors.toMap(WorkBaseInfoEntity::getId, WorkBaseInfoEntity::getStatus));
            // 获取Map中的key集合
            Set<Long> workIdSet = workIdAndStatusMap.keySet();
            // 如果key集合为空，返回
            if (CollUtil.isEmpty(workIdSet)) {
                return;
            }
            // 查询工单ID在key集合中的数据
            list = this.lambdaQuery()
                    .in(WorkOperationInfoEntity::getWorkId, workIdSet)
                    .list();

            // 如果查询结果为空，返回
            if (CollUtil.isEmpty(list)) {
                return;
            }
        }
        // 将Map赋值给finalMap
        Map<Long, Integer> finalMap = workIdAndStatusMap;
        // 遍历查询结果
        list.stream()
                .filter(Objects::nonNull)
                .forEach(entity -> {
                    // 计算超时时间
                    Date repairTime = Optional.ofNullable(entity.getRepairTime()).orElse(Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
                    // 计算超时时间
                    calculateOvertime(entity.getPreRepairTime(), entity, repairTime, finalMap);
                });

        // 更新查询结果
        this.updateBatchById(list);

    }


    private void calculateOvertime(Date preRepairTime, WorkOperationInfoEntity entity, Date repaireTime, Map<Long, Integer> workIdAndStatusMap) {
        // 计算超时时间
        Optional.ofNullable(preRepairTime)
                .map(time -> time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                .ifPresent(preRepairDate -> {
                    LocalDate date = repaireTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate plusDays = preRepairDate.plusDays(1);
                    LocalDate minusDays = preRepairDate.minusDays(1);
                    Integer workStatus = workIdAndStatusMap.getOrDefault(entity.getWorkId(), 0);
                    // 判断当前时间是否在超时之前，且超时之前是否完成
                    if (date.isBefore(plusDays) && isAfterOrEqual(date, minusDays) && !COMPLETED.getValue().equals(workStatus)) {
                        entity.setOvertime(ABOUT_TO_TIMEOUT.getName());
                        entity.setOvertimed(ABOUT_TO_TIMEOUT.getValue());
                    } else if (isAfterOrEqual(date, plusDays) && !COMPLETED.getValue().equals(workStatus)) {
                        // 计算超时时间
                        long dayDiff = ChronoUnit.DAYS.between(plusDays, date);
                        entity.setOvertime(++dayDiff + "天");
                        entity.setOvertimed(WorkOrderOvertimeEnum.TIMEOUT.getValue());
                    } else if (!WorkOrderOvertimeEnum.TIMEOUT.getValue().equals(entity.getOvertimed())) {
                        entity.setOvertime("-");
                        entity.setOvertimed(WorkOrderOvertimeEnum.NORMAL.getValue());
                    }
                });
    }

    private boolean isAfterOrEqual(LocalDate date1, LocalDate date2) {
        // 比较date1和date2的大小，返回true表示date1大于等于date2
        return date1.isAfter(date2) || date1.isEqual(date2);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateList(Collection<WorkOperationInfoVO> list) {
        // 遍历list，对每一个元素调用update方法
        list.forEach(this::update);
    }

}