package com.botong.workorder.vo;

import com.botong.api.module.system.vo.OrgWithUserVO;
import com.botong.framework.common.utils.DateUtils;
import com.botong.workorder.dto.WorkOrderAssignDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> by zhb on 2023/8/16.
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "工单申请")
public class WorkOrderBaseDetailsVO {

    @Schema(description = "工单id")
    private Long workId;

    @Schema(description = "电站编号")
    @NotNull(message = "电站编号不能为空")
    private String plantId;

    @Schema(description = "提单人id")
    private Long upUserId;

    @Schema(description = "提单人姓名")
    private String upUserName;

    @Schema(description = "提单时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date upWorkTime;

    @Schema(description = "工单来源：0->光伏系统，1->人工导入，2->客户投诉")
    @Range(min = 0, max = 2, message = "工单来源不正确")
    private Integer source;

    @Schema(description = "电站用户名称")
    private String plantUserName;

    @Schema(description = "电站地址")
    private String address;

    @Schema(description = "逆变器id")
    private String inverterId;

    @Schema(description = "电站用户电话")
    private String userPhone;

    @Schema(description = "电站归属机构ID")
    private Integer plantType;

    @Schema(description = "电站归属机构名称")
    private String plantTypeName;

    @Schema(description = "故障现象：0->电网侧异常，1->发电侧异常，2->通讯异常，3->漏水，4->其它")
    @Range(min = 0, max = 50, message = "故障现象不正确")
    private Integer alarmInfo;

    @Schema(description = "光云告警信息")
    private String alarmStr;

    @Schema(description = "故障类型：0->电网，1->设备，2->通讯，3->漏水 4->其他")
    @Range(min = 0, max = 4, message = "电站类型不正确")
    private Integer alarmType;

    @Schema(description = "故障设备：0->无故障，1->逆变器，2->运维器，3->断路器，4->失压开关，5->防雷器，6->结构件，7->电缆接插件，8->电表，9->电缆，10->光伏组件 11->通讯设备/模块")
    @Range(min = 0, max = 50, message = "故障设备不正确")
    private Integer alarmDevice;

    @Schema(description = "质保内容")
    private String warrantyContent;

    @Schema(description = "质保到期时间")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date warrantyExpireDate;

    @Schema(description = "审核人id")
    private Long examineUserId;

    @Schema(description = "审核人姓名")
    private String examineUserName;

    @Schema(description = "故障时间")
    @NotNull(message = "故障时间不能为空")
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    private Date alarmTime;

    @Schema(description = "故障等级：0->一般故障，1->严重故障，2->重大故障")
    @Range(min = 0, max = 2, message = "故障等级不正确")
    private Integer alarmLevel;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "备注类型")
    private Integer remarkType;

    @Schema(description = "维修状态")
    private Integer repairStatus;
    @Schema(description = "电站平台")
    private Integer platform;
    @JsonFormat(pattern = DateUtils.DATE_PATTERN)
    @NotNull(message = "计划修复时间不能为空")
    @Schema(description = "计划修复时间，格式: yyyy-MM-dd")
    Date preRepairTime;
    List<OrgWithUserVO> orgWithUsers;
    WorkOrderAssignDTO workOrderAssignDTO;
    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "电站状态")
    private Integer plantStatus;

    /**
     * 国家
     */
    private String country;

    /**
     * 省
     */
    private String province;

    /**
     * 市/州
     */
    private String city;

    /**
     * 县/区
     */
    private String area;

    /**
     * 镇/街道
     */
    private String town;


    @Schema(description = "故障报修照片路径")
    private String issuePhoto;
}