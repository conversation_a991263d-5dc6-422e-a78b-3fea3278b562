package com.botong.workorder.query;

import com.botong.framework.common.query.Query;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 告警信息查询参数
 * <AUTHOR> by zhb on 2023/9/11.
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PlantAlarmQuery extends Query {
    @Schema(description = "电站id")
    private String plantId;
    @Schema(description = "告警信息")
    private String alarmInfo;

    @Schema(description = "设备类型: 1:逆变器 2:运维器")
    private String deviceType;

    @Schema(description = "电站名称")
    private String plantName;

    @Schema(description = "是否去重")
    private Boolean isDistinct;

    @Schema(description = "数据来源：0:三晶 1:博通")
    private String source;

    @Schema(description = "报警启动时间的开始区间")
    private String startAlarmBeginTime;

    @Schema(description = "报警启动时间的结束区间")
    private String startAlarmFinishTime;

    @Schema(description = "是否生成工单: 0:未生成工单 1:已生成工单")
    @Range(min = 0, max = 1, message = "参数不正确")
    private Integer dispatchOrNot;

    @Schema(description = "电站类型")
    private Long plantType;

    @Schema(hidden = true)
    @JsonIgnore
    private List<String> projectSpecialList;

    @Schema(description = "状态（0实时-未处理，1历史-已处理 , 2失效）")
    private Integer status;
}