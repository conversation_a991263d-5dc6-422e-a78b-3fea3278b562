package com.botong.workorder.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.botong.api.module.workorder.entity.OrgBaseEntity;
import com.botong.framework.mybatis.dao.BaseDao;
import com.botong.workorder.entity.PlantBaseInfoEntity;
import com.botong.workorder.query.LowPowerQuery;
import com.botong.workorder.vo.LowPowerPlantVO;
import com.botong.workorder.vo.SiteDiagramVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.HashMap;
import java.util.List;

/**
 * 电站管理
 *
 * <AUTHOR> by zhb on 2023/8/16.
 */
@Mapper
public interface PlantBaseInfoDao extends BaseDao<PlantBaseInfoEntity> {

    List<OrgBaseEntity> getAllProject();

    @Update("update sys_org set name = #{newName} where name = #{oldName}")
    void updateProjectByName(@Param("oldName") String oldName, @Param("newName") String newName);

    List<String> transOrgId(@Param("orgIds") List<Long> orgIds);

    List<String> getPlantTypeByOrgId(HashMap<String, Object> params);

    String getContractIdByPlantId(@Param("plantId") String plantId);

    List<String> getElectricalDiagram(@Param("electricalTable") String electricalTable, @Param("contractId") String contractId);

    List<String> getSiteDiagramYx(@Param("siteTable") String siteTable, @Param("contractId") String contractId);

    List<SiteDiagramVO> getSiteDiagram(@Param("siteTable") String siteTable, @Param("contractId") String contractId);

    List<String> getOrgIdByProjectIds(@Param("plantType") Long plantType);

    @DS("slave")
    Page<LowPowerPlantVO> getLowPowerPlant(@Param("scope") List<String> scope, @Param("orgIds") List<String> orgIds, Page<LowPowerPlantVO> page, @Param("query") LowPowerQuery query);

    String getAlarmStr(@Param("plantId") String plantId);
}