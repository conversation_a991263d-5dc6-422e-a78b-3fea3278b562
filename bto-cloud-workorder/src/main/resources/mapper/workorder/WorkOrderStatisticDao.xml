<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.WorkOrderStatisticDao">

    <!--条件查询-->
    <sql id="queryConditions">

        <if test="user != null and user.superAdmin == 0">
            <if test="user.dataScopeList != null and user.dataScopeList.size() > 0">
                AND ( v.org_id in
                <foreach collection="user.dataScopeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                <if test="user.id != null">
                    OR v.up_user_id = #{user.id} )
                </if>
            </if>
        </if>

        <if test="workIdList !=null and workIdList.size() > 0">
            AND v.work_id in
            <foreach collection="workIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="query != null">
            <if test="query.alarmType != null and query.alarmType.size() > 0">
                AND v.alarm_type IN
                <foreach item="item" collection="query.alarmType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.status != null and query.status.size() > 0">
                AND v.status IN
                <foreach item="item" collection="query.status" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.platform != null">
                AND v.platform = #{query.platform}

            </if>
            <if test="query.country != null  and query.country.size() > 0">
                AND v.country IN
                <foreach item="item" collection="query.country" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.province != null  and query.province.size() > 0">
                AND v.province IN
                <foreach item="item" collection="query.province" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.city != null  and query.city.size() > 0">
                AND v.city IN
                <foreach item="item" collection="query.city" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


            <if test="query.overtimed !=null and query.overtimed.size() > 0">
                AND v.overtimed in
                <foreach collection="query.overtimed" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.startTime != null and query.endTime != null ">
                <choose>
                    <when test="query.startTime.length() == 4">
                        AND DATE_FORMAT(v.create_time, '%Y') BETWEEN #{query.startTime} AND #{query.endTime}
                    </when>
                    <when test="query.startTime.length() == 7">
                        AND DATE_FORMAT(v.create_time, '%Y-%m') BETWEEN #{query.startTime} AND #{query.endTime}
                    </when>
                    <when test="query.startTime.length() == 10">
                        AND DATE_FORMAT(v.create_time, '%Y-%m-%d') BETWEEN #{query.startTime} AND #{query.endTime}
                    </when>
                </choose>
            </if>
        </if>
    </sql>

    <select id="statistics" resultType="com.botong.workorder.vo.StatisticsVO">
        SELECT t1.dict_value, t1.dict_label,
        <if test="dictTypeId == 15">
            COUNT(t2.alarm_type)
        </if>
        <if test="dictTypeId == 16">
            COUNT(t2.status)
        </if>
        AS count
        FROM sys_dict_data t1
        LEFT JOIN (
        SELECT status,alarm_type,org_id
        FROM work_order_process_manage_view v
        <where>
            <include refid="queryConditions"/>
            <if test="query.plantId !=null and query.plantId.trim() != ''">
                AND v.plant_id = #{query.plantId}
            </if>
            <if test="scopeList != null and scopeList.size() > 0">
                AND org_id IN
                <foreach collection="scopeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) t2
        ON
        <if test="dictTypeId ==15">
            t2.alarm_type = t1.dict_value
        </if>
        <if test="dictTypeId ==16">
            t2.status = t1.dict_value
        </if>
        <where>
            <if test="dictTypeId != null">
                AND t1.dict_type_id = #{dictTypeId}
                AND t1.deleted = 0
            </if>

        </where>
        GROUP BY t1.dict_value, t1.dict_label;
    </select>

    <!-- 在映射文件中定义递归查询 -->
    <select id="region" resultType="com.botong.workorder.vo.RegionVO">
        SELECT country, province, city
        FROM work_order_process_manage_view v
        <where>
            <if test="workIdList !=null and workIdList.size() > 0">
                AND v.work_id in
                <foreach collection="workIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="user != null and user.superAdmin == 0">
                <if test="user.dataScopeList != null and user.dataScopeList.size() > 0">
                    AND ( v.org_id in
                    <foreach collection="user.dataScopeList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    <if test="user.id != null">
                        OR v.up_user_id = #{user.id} )
                    </if>
                </if>
            </if>
        </where>
    </select>

    <select id="regionCount" resultType="com.botong.workorder.vo.RegionCount">
        SELECT
        <include refid="regionCount"/>
        as dictLabel, COUNT(*) AS count
        FROM work_order_process_manage_view v
        <where>
            <include refid="queryConditions"/>
            <if test="scopeList != null and scopeList.size() > 0">
                AND org_id IN
                <foreach collection="scopeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        <include refid="regionCount"/>
    </select>
    <select id="getElectricChargeAnalyze" resultType="com.botong.workorder.vo.ElectricChargeAnalyzeVO">
        SELECT
            pb.city,
            COUNT( DISTINCT pb.plant_uid ) AS plantNumber,
            SUM( bb.electricity ) AS electricity,
            SUM( pb.plant_capacity ) AS totalCapacity,
            peh.${monthField} AS monthEffectiveHours,
            SUM( bb.electricity ) / NULLIF( SUM( pb.plant_capacity ), 0 ) AS monthActualEffectiveHours,
            CASE
                WHEN ( SUM( bb.electricity ) / NULLIF( SUM( pb.plant_capacity ), 0 ) - peh.${monthField} ) > 0 THEN
                    '发电量较好'
                WHEN ( SUM( bb.electricity ) / NULLIF( SUM( pb.plant_capacity ), 0 ) - peh.${monthField} ) > - 5 THEN
                    '发电量一般'
                WHEN ( SUM( bb.electricity ) / NULLIF( SUM( pb.plant_capacity ), 0 ) - peh.${monthField} ) > - 10 THEN
                    '发电量较低' ELSE '发电量异常'
                END AS analyzeResult
        FROM
            bill_bto_electricity_summary bb
                JOIN v_plant_base pb ON pb.meter_id = bb.meter_id
                AND bb.settlement_period LIKE #{yearMonth}
                JOIN plant_month_equivalent_hour peh ON pb.city = peh.city
        GROUP BY
            pb.city
    </select>
    <select id="getPlantElectricChargeAnalyze" resultType="com.botong.workorder.vo.ElectricChargeAnalyzeVO">
        SELECT
        bbes.settlement_period,
        vpb.plant_name,
        bbes.electricity electricitySettlement,
        vpb.city,
        vpb.plant_capacity,
        ( vpm.electricity / 100 ) / vpb.plant_capacity gYEffectiveHours,
        vpb.create_time,
        bbes.electricity / vpb.plant_capacity gDEffectiveHours,
        pmeh.${query.monthField} monthEffectiveHours,
        CASE
        WHEN DATEDIFF( LAST_DAY( STR_TO_DATE( CONCAT( #{query.yearMonth}, '-01' ), '%Y-%m-%d' )), vpb.create_time ) &lt; 31 THEN NULL WHEN bbes.electricity / vpb.plant_capacity > pmeh.august * 0.8 THEN
        '正常发电' ELSE '发电异常'
        END AS analyzeResult
        FROM
        v_plant_base vpb
        LEFT JOIN bill_bto_electricity_summary bbes ON vpb.meter_id = bbes.meter_id
        LEFT JOIN v_plant_month vpm ON vpb.plant_uid = vpm.plant_uid
        AND vpm.collect LIKE #{query.yearMonth}
        LEFT JOIN plant_month_equivalent_hour pmeh ON vpb.city = pmeh.city
        WHERE
        bbes.settlement_period LIKE #{query.yearMonth}
        <if test="query.city != null and query.city != ''">
            AND vpb.city = #{query.city}
        </if>
        <if test="query.plantName != null and query.plantName != ''">
            AND vpb.plant_name = #{query.plantName}
        </if>
        GROUP BY
        vpb.plant_name
        <if test="query.analyzeResult != null and query.analyzeResult != ''">
            HAVING analyzeResult = #{query.analyzeResult}
        </if>
        ORDER BY
        vpb.create_time DESC
    </select>

    <sql id="regionCount">
        <if test="type != null and type.length > 0">
            <if test="type == 'country'">
                v.province
            </if>
            <if test="type == 'province'">
                v.city
            </if>
            <if test="type == 'city'">
                v.area
            </if>
            <if test="type == 'area'">
                v.town
            </if>
        </if>
    </sql>
</mapper>