<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.botong.workorder.dao.PlantAlarmDao">


    <select id="getPageList" resultType="com.botong.api.module.photovoltaic.vo.PlantAlarmVO">
        WITH named_query AS (
        SELECT
        v_plant_alarm.id,
        v_plant_alarm.plant_name,
        v_plant_alarm.plant_uid,
        v_plant_alarm.alarm_level,
        v_plant_alarm.device_id,
        v_plant_alarm.device_type,
        v_plant_alarm.start_time,
        v_plant_alarm.alarm_mean,
        v_plant_alarm.`status`,
        v_plant_alarm.source,
        v_plant_alarm.city,
        plant_base_info.create_time,
        v_plant_alarm.project_special,
        CASE

        WHEN v_plant_alarm.plant_uid IN ( SELECT plant_id FROM work_order_process_manage_view WHERE `status` != 3 ) THEN
        1 ELSE 0
        END AS dispatchOrNot,
        CASE

        WHEN v_plant_alarm.plant_uid IN ( SELECT plant_id FROM work_order_process_manage_view WHERE `status` != 3 ) THEN
        wopmv.work_id ELSE NULL
        END AS work_id
        FROM
        v_plant_alarm
        LEFT JOIN plant_base_info ON v_plant_alarm.plant_uid = plant_base_info.id
        LEFT JOIN work_order_process_manage_view wopmv ON v_plant_alarm.plant_uid = wopmv.plant_id
        AND wopmv.`status` != 3
        GROUP BY
        v_plant_alarm.plant_name
        ORDER BY
        v_plant_alarm.start_time DESC
        ) SELECT
        named_query.*,
        v_project_category.name projectName
        FROM
        named_query left join v_project_category ON named_query.project_special = v_project_category.`id`
        <where>
            <if test="query.alarmInfo != null and query.alarmInfo != ''">
                AND alarm_mean LIKE CONCAT('%',#{query.alarmInfo},'%')
            </if>
            <if test="query.plantName != null and query.plantName != ''">
                AND plant_name LIKE CONCAT('%',#{query.plantName},'%')
            </if>
            <if test="query.dispatchOrNot != null">
                AND dispatchOrNot = #{query.dispatchOrNot}
            </if>
            <if test="query.projectSpecialList != null and query.projectSpecialList.size() > 0">
                AND project_special IN
                <foreach item="item" collection="query.projectSpecialList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>